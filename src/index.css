@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 220 13% 9%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 9%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 221 100% 65%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 221 83% 53%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    
    /* eSIM marketplace specific tokens */
    --connectivity-primary: 221 83% 53%;
    --connectivity-secondary: 200 98% 45%;
    --success-green: 142 76% 36%;
    --warning-orange: 25 95% 53%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--connectivity-primary)), hsl(var(--connectivity-secondary)));
    --gradient-card: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(var(--connectivity-primary) / 0.1) 0%, hsl(var(--connectivity-secondary) / 0.05) 100%);
    
    /* Shadows */
    --shadow-card: 0 4px 6px -1px hsl(var(--connectivity-primary) / 0.1), 0 2px 4px -1px hsl(var(--connectivity-primary) / 0.06);
    --shadow-card-hover: 0 10px 15px -3px hsl(var(--connectivity-primary) / 0.1), 0 4px 6px -2px hsl(var(--connectivity-primary) / 0.05);
    --shadow-glow: 0 0 20px hsl(var(--primary-glow) / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}