import { useState } from 'react';
import { Check, Wifi, Calendar, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface PlanCardProps {
  id: string;
  name: string;
  type: 'unlimited' | 'fixed';
  data?: string;
  validity: string;
  price: number;
  currency: string;
  features: string[];
  popular?: boolean;
  onSelect: (planId: string) => void;
}

export function PlanCard({
  id,
  name,
  type,
  data,
  validity,
  price,
  currency,
  features,
  popular = false,
  onSelect,
}: PlanCardProps) {
  const [isSelected, setIsSelected] = useState(false);

  const handleSelect = () => {
    setIsSelected(true);
    onSelect(id);
  };

  return (
    <Card className={`relative hover:shadow-card-hover transition-all duration-300 ${
      popular ? 'border-primary shadow-glow' : ''
    } ${isSelected ? 'border-accent bg-accent/5' : ''}`}>
      {popular && (
        <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-primary text-primary-foreground">
          Most Popular
        </Badge>
      )}
      
      <CardHeader className="text-center pb-4">
        <CardTitle className="flex items-center justify-center gap-2">
          {type === 'unlimited' ? (
            <Zap className="h-5 w-5 text-accent" />
          ) : (
            <Wifi className="h-5 w-5 text-connectivity-primary" />
          )}
          {name}
        </CardTitle>
        <div className="text-3xl font-bold text-primary">
          {currency === 'JPY' ? '¥' : '$'}{price}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Wifi className="h-4 w-4 text-connectivity-secondary" />
            <span className="font-medium">
              {type === 'unlimited' ? 'Unlimited Data' : data}
            </span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-connectivity-secondary" />
            <span>Valid for {validity}</span>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium text-sm">Features included:</h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                <Check className="h-3 w-3 text-success" />
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>

      <CardFooter>
        <Button 
          variant={isSelected ? "checkout" : "plan"} 
          className="w-full"
          onClick={handleSelect}
          disabled={isSelected}
        >
          {isSelected ? 'Selected' : 'Select Plan'}
        </Button>
      </CardFooter>
    </Card>
  );
}