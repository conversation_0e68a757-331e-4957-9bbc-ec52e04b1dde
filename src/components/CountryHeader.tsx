import japanFlag from '@/assets/japan-flag.png';

interface CountryHeaderProps {
  countryName: string;
  description: string;
}

export function CountryHeader({ countryName, description }: CountryHeaderProps) {
  return (
    <div className="bg-gradient-hero rounded-lg p-8 mb-8 text-center">
      <div className="flex items-center justify-center mb-4">
        <img 
          src={japanFlag} 
          alt="Japan flag" 
          className="w-16 h-16 rounded-lg shadow-card animate-float"
        />
      </div>
      <h1 className="text-4xl font-bold text-foreground mb-2">
        eSIM for {countryName}
      </h1>
      <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
        {description}
      </p>
    </div>
  );
}