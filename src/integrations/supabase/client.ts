// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://shpgbjbxlrizdvkitrup.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocGdiamJ4bHJpemR2a2l0cnVwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMTc3NjEsImV4cCI6MjA2Nzc5Mzc2MX0.CUWZLnsIa7-cMeC_biewI2A0SrvI5uiii4Xh_G0ZFys";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});