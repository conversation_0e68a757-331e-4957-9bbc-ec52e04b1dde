import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CountryHeader } from '@/components/CountryHeader';
import { PlanCard } from '@/components/PlanCard';
import { LanguageSelector } from '@/components/LanguageSelector';

const plans = [
  {
    id: 'unlimited-7',
    name: 'Unlimited 7 Days',
    type: 'unlimited' as const,
    validity: '7 days',
    price: 2980,
    currency: 'JPY',
    features: [
      'Unlimited high-speed data',
      '5G network access',
      'Instant activation',
      'No speed throttling',
      'Works with all carriers'
    ],
    popular: false,
  },
  {
    id: 'unlimited-15',
    name: 'Unlimited 15 Days',
    type: 'unlimited' as const,
    validity: '15 days',
    price: 4980,
    currency: 'JPY',
    features: [
      'Unlimited high-speed data',
      '5G network access',
      'Instant activation',
      'No speed throttling',
      'Works with all carriers',
      'Best value for 2+ weeks'
    ],
    popular: true,
  },
  {
    id: 'fixed-5gb',
    name: '5GB Data Plan',
    type: 'fixed' as const,
    data: '5GB',
    validity: '30 days',
    price: 1980,
    currency: 'JPY',
    features: [
      '5GB high-speed data',
      '4G/5G network access',
      'Instant activation',
      'Data top-up available',
      'Perfect for light usage'
    ],
    popular: false,
  },
  {
    id: 'fixed-10gb',
    name: '10GB Data Plan',
    type: 'fixed' as const,
    data: '10GB',
    validity: '30 days',
    price: 3480,
    currency: 'JPY',
    features: [
      '10GB high-speed data',
      '4G/5G network access',
      'Instant activation',
      'Data top-up available',
      'Great for moderate usage'
    ],
    popular: false,
  },
];

export default function Japan() {
  const navigate = useNavigate();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
    // Navigate to checkout after a brief delay to show selection
    setTimeout(() => {
      navigate('/checkout', { state: { planId } });
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/')}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
              <div className="text-lg font-semibold">eSIM Marketplace</div>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <CountryHeader
          countryName="Japan"
          description="Stay connected throughout Japan with our premium eSIM plans. Enjoy seamless coverage across all major cities and rural areas with instant activation."
        />

        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Choose Your Plan</h2>
          <p className="text-muted-foreground">
            Select the perfect eSIM plan for your trip to Japan. All plans include instant activation and work with unlocked devices.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {plans.map((plan) => (
            <PlanCard
              key={plan.id}
              {...plan}
              onSelect={handlePlanSelect}
            />
          ))}
        </div>

        <div className="mt-12 bg-card rounded-lg p-6 shadow-card">
          <h3 className="text-xl font-semibold mb-4">Why Choose Our eSIM?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">⚡</span>
              </div>
              <h4 className="font-medium mb-2">Instant Activation</h4>
              <p className="text-sm text-muted-foreground">
                Get connected within minutes of purchase
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🌐</span>
              </div>
              <h4 className="font-medium mb-2">Wide Coverage</h4>
              <p className="text-sm text-muted-foreground">
                Works with all major Japanese carriers
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🔒</span>
              </div>
              <h4 className="font-medium mb-2">Secure & Reliable</h4>
              <p className="text-sm text-muted-foreground">
                Trusted by thousands of travelers
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}