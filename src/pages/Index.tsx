import { useNavigate } from 'react-router-dom';
import { Wifi, Globe, Zap, Shield, ArrowRight, CheckCircle, Users, MapPin, MessageCircle, Star, Clock, Download, Smartphone } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { LanguageSelector } from '@/components/LanguageSelector';

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Wifi className="h-5 w-5 text-primary-foreground" />
              </div>
              <div className="text-xl font-bold">OHESIM</div>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-hero py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-accent/10 animate-pulse" />
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            {/* Social Proof */}
            <div className="flex items-center justify-center gap-6 mb-8 animate-fade-in">
              <Badge variant="secondary" className="gap-2">
                <Users className="h-4 w-4" />
                50,000+ Travelers Connected
              </Badge>
              <Badge variant="secondary" className="gap-2">
                <MapPin className="h-4 w-4" />
                200+ Countries
              </Badge>
            </div>
            
            <h1 className="text-6xl md:text-7xl font-bold mb-6 animate-fade-in">
              Stay Connected
              <span className="bg-gradient-primary bg-clip-text text-transparent"> Anywhere</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 animate-fade-in max-w-2xl mx-auto">
              Get instant eSIM connectivity for your travels. No physical SIM cards needed.
              Activate in minutes and start exploring with confidence.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button 
                variant="hero" 
                size="lg" 
                onClick={() => navigate('/japan')}
                className="gap-2 animate-scale-in"
              >
                Explore Japan eSIMs
                <ArrowRight className="h-5 w-5" />
              </Button>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Free setup • No commitments
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground animate-fade-in">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span>4.9/5 Rating</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>24/7 Support</span>
              </div>
              <div className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                <span>Instant Download</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">How It Works</h2>
            <p className="text-lg text-muted-foreground">
              Get connected in 3 simple steps
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold text-primary-foreground">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">Choose Your Plan</h3>
              <p className="text-muted-foreground">
                Select the perfect data plan for your destination and travel duration.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold text-primary-foreground">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">Install eSIM</h3>
              <p className="text-muted-foreground">
                Scan the QR code and install your eSIM instantly on your device.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold text-primary-foreground">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">Stay Connected</h3>
              <p className="text-muted-foreground">
                Enjoy seamless connectivity without roaming charges.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Why Choose Our eSIM?</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Experience seamless connectivity with our premium eSIM solutions designed for modern travelers.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center hover:shadow-card-hover transition-all duration-300 hover:scale-105">
              <CardContent className="pt-8">
                <div className="w-16 h-16 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Instant Activation</h3>
                <p className="text-muted-foreground">
                  Get connected within minutes. No waiting, no physical SIM cards to swap.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-card-hover transition-all duration-300 hover:scale-105">
              <CardContent className="pt-8">
                <div className="w-16 h-16 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Global Coverage</h3>
                <p className="text-muted-foreground">
                  Connect to local networks worldwide with premium carrier partnerships.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-card-hover transition-all duration-300 hover:scale-105">
              <CardContent className="pt-8">
                <div className="w-16 h-16 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Secure & Reliable</h3>
                <p className="text-muted-foreground">
                  Enterprise-grade security with 99.9% uptime guarantee.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-card-hover transition-all duration-300 hover:scale-105">
              <CardContent className="pt-8">
                <div className="w-16 h-16 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Wifi className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">5G Ready</h3>
                <p className="text-muted-foreground">
                  Experience blazing-fast 5G speeds wherever available.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">What Our Travelers Say</h2>
            <p className="text-lg text-muted-foreground">
              Join thousands of satisfied customers worldwide
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="p-6">
              <CardContent className="pt-0">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "Perfect for my trip to Tokyo! Setup was incredibly easy and the connection was flawless throughout my journey."
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
                    S
                  </div>
                  <div>
                    <div className="font-semibold">Sarah Chen</div>
                    <div className="text-sm text-muted-foreground">Tokyo, Japan</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="p-6">
              <CardContent className="pt-0">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "Saved me so much money compared to roaming charges. The data speeds were excellent for all my business needs."
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
                    M
                  </div>
                  <div>
                    <div className="font-semibold">Michael Rodriguez</div>
                    <div className="text-sm text-muted-foreground">Osaka, Japan</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="p-6">
              <CardContent className="pt-0">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "Customer support was amazing when I had questions. The whole family stayed connected without any issues."
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
                    A
                  </div>
                  <div>
                    <div className="font-semibold">Anna Thompson</div>
                    <div className="text-sm text-muted-foreground">Kyoto, Japan</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Countries Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Available Destinations</h2>
            <p className="text-lg text-muted-foreground">
              Start with Japan, more countries coming soon!
            </p>
          </div>

          <div className="max-w-md mx-auto">
            <Card className="hover:shadow-card-hover transition-all duration-300 cursor-pointer group" 
                  onClick={() => navigate('/japan')}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-4xl">🇯🇵</div>
                    <div>
                      <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
                        Japan
                      </h3>
                      <p className="text-muted-foreground">
                        Unlimited & fixed data plans
                      </p>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-muted-foreground">
              Everything you need to know about eSIMs
            </p>
          </div>
          
          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible>
              <AccordionItem value="item-1">
                <AccordionTrigger>What is an eSIM and how does it work?</AccordionTrigger>
                <AccordionContent>
                  An eSIM (embedded SIM) is a digital SIM card that allows you to connect to a cellular network without a physical SIM card. You simply scan a QR code to download and install the eSIM profile on your compatible device.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-2">
                <AccordionTrigger>Which devices support eSIM?</AccordionTrigger>
                <AccordionContent>
                  Most modern smartphones support eSIM including iPhone XS and newer, Google Pixel 3 and newer, Samsung Galaxy S20 and newer, and many other flagship devices. Check your device compatibility before purchasing.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-3">
                <AccordionTrigger>How quickly can I get connected?</AccordionTrigger>
                <AccordionContent>
                  After purchase, you'll receive your eSIM QR code instantly via email. Installation takes just a few minutes by scanning the code. You can activate your plan immediately upon arrival at your destination.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-4">
                <AccordionTrigger>What happens if I run out of data?</AccordionTrigger>
                <AccordionContent>
                  For unlimited plans, you'll never run out of data. For fixed data plans, you can easily purchase additional data top-ups through our platform or buy a new plan if needed.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-5">
                <AccordionTrigger>Can I use my eSIM in multiple countries?</AccordionTrigger>
                <AccordionContent>
                  Our eSIMs are country-specific for the best performance and pricing. We're working on regional plans that will work across multiple countries. Check back soon for updates!
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-6">
                <AccordionTrigger>Is there customer support available?</AccordionTrigger>
                <AccordionContent>
                  Yes! We offer 24/7 customer support via chat and email. Our team is always ready to help you with installation, troubleshooting, or any questions about your eSIM service.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border bg-card py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Wifi className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="text-lg font-semibold">OHESIM</div>
          </div>
          <p className="text-muted-foreground">
            © 2024 OHESIM. Connecting travelers worldwide.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
