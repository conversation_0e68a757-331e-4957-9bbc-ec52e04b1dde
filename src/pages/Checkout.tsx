import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft, Shield, CreditCard, User, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { LanguageSelector } from '@/components/LanguageSelector';

// Mock plan data - in real app this would come from your CMS/API
const planDetails = {
  'unlimited-7': {
    name: 'Unlimited 7 Days',
    type: 'unlimited',
    validity: '7 days',
    price: 2980,
    currency: 'JPY',
    data: 'Unlimited'
  },
  'unlimited-15': {
    name: 'Unlimited 15 Days',
    type: 'unlimited',
    validity: '15 days',
    price: 4980,
    currency: 'JPY',
    data: 'Unlimited'
  },
  'fixed-5gb': {
    name: '5GB Data Plan',
    type: 'fixed',
    validity: '30 days',
    price: 1980,
    currency: 'JPY',
    data: '5GB'
  },
  'fixed-10gb': {
    name: '10GB Data Plan',
    type: 'fixed',
    validity: '30 days',
    price: 3480,
    currency: 'JPY',
    data: '10GB'
  },
};

export default function Checkout() {
  const navigate = useNavigate();
  const location = useLocation();
  const planId = location.state?.planId || 'unlimited-15';
  const plan = planDetails[planId as keyof typeof planDetails];
  
  const [isGuest, setIsGuest] = useState(true);
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePayment = () => {
    // In real implementation, this would integrate with Stripe
    console.log('Processing payment for:', { planId, formData, isGuest });
    // Simulate payment processing
    alert('Payment integration with Stripe would happen here!');
  };

  if (!plan) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Plan Not Found</h1>
          <Button onClick={() => navigate('/japan')}>
            Back to Plans
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/japan')}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Plans
              </Button>
              <div className="text-lg font-semibold">Checkout</div>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Checkout Form */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="guest-mode"
                    checked={isGuest}
                    onCheckedChange={setIsGuest}
                  />
                  <Label htmlFor="guest-mode">Checkout as guest</Label>
                </div>

                {!isGuest && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      Sign in to save your purchase history and access exclusive offers.
                    </p>
                    <Button variant="outline" size="sm" className="mt-2">
                      Sign In
                    </Button>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      placeholder="Enter first name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      placeholder="Enter last name"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      className="pl-10"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="phone">Phone Number (Optional)</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="phone"
                      type="tel"
                      className="pl-10"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+81 90 1234 5678"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-muted rounded-lg text-center">
                  <Shield className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <p className="font-medium">Secure Payment with Stripe</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Your payment information is encrypted and secure
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium">{plan.name}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Data</span>
                    <span>{plan.data}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Validity</span>
                    <span>{plan.validity}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Country</span>
                    <span>Japan</span>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>¥{plan.price}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Tax</span>
                    <span>¥0</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>¥{plan.price}</span>
                  </div>
                </div>

                <Button 
                  variant="hero" 
                  size="lg" 
                  className="w-full"
                  onClick={handlePayment}
                >
                  Complete Purchase
                </Button>

                <div className="text-xs text-muted-foreground text-center space-y-1">
                  <p>
                    ✓ Instant delivery via email
                  </p>
                  <p>
                    ✓ 24/7 customer support
                  </p>
                  <p>
                    ✓ 30-day money-back guarantee
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}